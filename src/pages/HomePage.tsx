import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { CursorService } from "../services/cursorService";
import { AccountService } from "../services/accountService";
import { LoadingSpinner } from "../components/LoadingSpinner";
import { Button } from "../components/Button";

export const HomePage: React.FC = () => {
  const [cursorInstalled, setCursorInstalled] = useState<boolean | null>(null);
  const [cursorPaths, setCursorPaths] = useState<[string, string] | null>(null);
  const [loading, setLoading] = useState(true);
  const [debugInfo, setDebugInfo] = useState<string[]>([]);
  const [showDebug, setShowDebug] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [deleteMessage, setDeleteMessage] = useState<string | null>(null);

  useEffect(() => {
    checkCursorInstallation();
  }, []);

  const checkCursorInstallation = async () => {
    try {
      setLoading(true);
      const installed = await CursorService.checkCursorInstallation();
      setCursorInstalled(installed);

      if (installed) {
        const paths = await CursorService.getCursorPaths();
        setCursorPaths(paths);
      } else {
        const debug = await CursorService.debugCursorPaths();
        setDebugInfo(debug);
      }
    } catch (error) {
      console.error("检查 Cursor 安装失败:", error);
      setCursorInstalled(false);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteAccount = async () => {
    // if (!confirm("确定要删除账户吗？此操作不可逆！")) {
    //   return;
    // }

    try {
      setDeleteLoading(true);
      setDeleteMessage(null);

      const result = await AccountService.deleteAccount();

      if (result.success) {
        setDeleteMessage(result.message);
      } else {
        setDeleteMessage("删除账户失败，请稍后重试");
      }
    } catch (error) {
      console.error("删除账户失败:", error);
      setDeleteMessage("删除账户时发生错误");
    } finally {
      setDeleteLoading(false);
    }
  };

  if (loading) {
    return <LoadingSpinner message="正在检查 Cursor 安装状态..." />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900">
          Cursor Machine ID Restorer
        </h1>
        <p className="mt-2 text-lg text-gray-600">
          管理和恢复 Cursor 的 Machine ID
        </p>
      </div>

      {/* Status Card */}
      <div className="p-6 bg-white rounded-lg shadow">
        <h2 className="mb-4 text-lg font-medium text-gray-900">
          🔍 Cursor 安装状态
        </h2>

        {cursorInstalled === true ? (
          <div className="space-y-4">
            <div className="flex items-center">
              <span className="mr-2 text-xl text-green-500">✅</span>
              <span className="font-medium text-green-700">Cursor 已安装</span>
            </div>

            {cursorPaths && (
              <div className="p-4 rounded-md bg-green-50">
                <h3 className="mb-2 font-medium text-green-800">安装路径:</h3>
                <div className="space-y-1 text-sm text-green-700">
                  <p>
                    <strong>应用路径:</strong> {cursorPaths[0]}
                  </p>
                  <p>
                    <strong>配置路径:</strong> {cursorPaths[1]}
                  </p>
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="space-y-4">
            <div className="flex items-center">
              <span className="mr-2 text-xl text-red-500">❌</span>
              <span className="font-medium text-red-700">
                未检测到 Cursor 安装
              </span>
            </div>

            <div className="p-4 rounded-md bg-red-50">
              <p className="mb-2 text-sm text-red-700">
                请确保 Cursor 已正确安装并至少运行过一次。
              </p>

              <Button
                variant="secondary"
                size="sm"
                onClick={() => setShowDebug(!showDebug)}
              >
                {showDebug ? "隐藏" : "显示"}调试信息
              </Button>

              {showDebug && debugInfo.length > 0 && (
                <div className="mt-3 space-y-1">
                  {debugInfo.map((info, index) => (
                    <p
                      key={index}
                      className="p-2 text-xs text-red-600 bg-red-100 rounded"
                    >
                      {info}
                    </p>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Action Cards */}
      {cursorInstalled && (
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          {/* Machine ID Management */}
          <div className="p-6 bg-white rounded-lg shadow">
            <div className="flex items-center mb-4">
              <span className="mr-3 text-2xl">🔧</span>
              <h3 className="text-lg font-medium text-gray-900">
                Machine ID 管理
              </h3>
            </div>
            <p className="mb-4 text-gray-600">
              查看、备份、恢复或重置 Cursor 的 Machine ID
            </p>
            <Link to="/machine-id">
              <Button variant="primary" className="w-full">
                进入管理
              </Button>
            </Link>
          </div>

          {/* Auth Check */}
          <div className="p-6 bg-white rounded-lg shadow">
            <div className="flex items-center mb-4">
              <span className="mr-3 text-2xl">🔐</span>
              <h3 className="text-lg font-medium text-gray-900">授权检查</h3>
            </div>
            <p className="mb-4 text-gray-600">
              检查 Cursor 账户授权状态和订阅信息
            </p>
            <Link to="/auth-check">
              <Button variant="primary" className="w-full">
                开始检查
              </Button>
            </Link>
          </div>

          {/* Delete Account Test */}
          <div className="p-6 bg-white rounded-lg shadow">
            <div className="flex items-center mb-4">
              <span className="mr-3 text-2xl">🗑️</span>
              <h3 className="text-lg font-medium text-gray-900">
                删除账户测试
              </h3>
            </div>
            <p className="mb-4 text-gray-600">测试 Cursor 删除账户 API 接口</p>
            <Button
              variant="secondary"
              className="w-full"
              onClick={handleDeleteAccount}
              loading={deleteLoading}
            >
              {deleteLoading ? "删除中..." : "测试删除账户"}
            </Button>

            {deleteMessage && (
              <div
                className={`mt-3 p-3 rounded-md text-sm ${
                  deleteMessage.includes("成功")
                    ? "bg-green-50 text-green-700 border border-green-200"
                    : "bg-red-50 text-red-700 border border-red-200"
                }`}
              >
                {deleteMessage}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Refresh Button */}
      <div className="text-center">
        <Button
          variant="secondary"
          onClick={checkCursorInstallation}
          loading={loading}
        >
          🔄 重新检查
        </Button>
      </div>
    </div>
  );
};
