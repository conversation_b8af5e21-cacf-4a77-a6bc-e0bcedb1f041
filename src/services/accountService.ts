import { invoke } from "@tauri-apps/api/core";
import type {
  Account<PERSON>ist<PERSON><PERSON>ult,
  SwitchAccount<PERSON><PERSON>ult,
  Add<PERSON><PERSON>unt<PERSON><PERSON>ult,
  EditAccountResult,
  RemoveAccountR<PERSON>ult,
  LogoutResult
} from "../types/account";

export class AccountService {
  // Get all accounts with current account info
  static async getAccountList(): Promise<AccountListResult> {
    return await invoke<AccountListResult>("get_account_list");
  }

  // Mock delete account API - simulates cursor.com/api/dashboard/delete-account
  static async deleteAccount(): Promise<{ success: boolean; message: string }> {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

    // Log the simulated API call details
    console.log('🔄 模拟 API 调用:');
    console.log('URL: https://cursor.com/api/dashboard/delete-account');
    console.log('Method: POST');
    console.log('Headers:', {
      'Content-Type': 'application/json',
      'Accept': '*/*',
      'Accept-Language': 'en,zh-CN;q=0.9,zh;q=0.8,eu;q=0.7',
      'Cache-Control': 'no-cache',
      'Origin': window.location.origin,
      'Referer': `${window.location.origin}/cn/dashboard?tab=settings`,
      'Sec-Fetch-Dest': 'empty',
      'Sec-Fetch-Mode': 'cors',
      'Sec-Fetch-Site': 'same-origin',
      'User-Agent': navigator.userAgent
    });
    console.log('Body: {}');

    // Simulate the response headers and status
    console.log('📥 模拟 API 响应:');
    console.log('Status: 200 OK');
    console.log('Response Headers:', {
      'cache-control': 'public, max-age=0, must-revalidate',
      'content-length': '2',
      'content-type': 'application/json; charset=utf-8',
      'etag': '"bwc9mymkdm2"',
      'server': 'Vercel',
      'x-matched-path': '/api/dashboard/delete-account'
    });
    console.log('Response Body: {}');

    // Simulate random success/failure for testing
    const shouldSucceed = Math.random() > 0.2; // 80% success rate

    if (shouldSucceed) {
      return {
        success: true,
        message: '✅ 模拟删除账户成功！API 调用已完成，返回状态码 200'
      };
    } else {
      return {
        success: false,
        message: '❌ 模拟删除账户失败！服务器返回错误 (随机测试失败)'
      };
    }
  }

  // Add a new account
  static async addAccount(email: string, token: string, refreshToken?: string): Promise<AddAccountResult> {
    return await invoke<AddAccountResult>("add_account", {
      email,
      token,
      refresh_token: refreshToken || null
    });
  }

  // Switch to a different account
  static async switchAccount(email: string): Promise<SwitchAccountResult> {
    return await invoke<SwitchAccountResult>("switch_account", { email });
  }

  // Switch to account using email and token directly (improved method)
  static async switchAccountWithToken(
    email: string,
    token: string,
    authType?: string
  ): Promise<SwitchAccountResult> {
    return await invoke<SwitchAccountResult>("switch_account_with_token", {
      email,
      token,
      authType
    });
  }

  // Edit an existing account
  static async editAccount(
    email: string,
    newToken?: string,
    newRefreshToken?: string
  ): Promise<EditAccountResult> {
    return await invoke<EditAccountResult>("edit_account", {
      email,
      newToken: newToken || null,
      newRefreshToken: newRefreshToken || null
    });
  }

  // Remove an account
  static async removeAccount(email: string): Promise<RemoveAccountResult> {
    return await invoke<RemoveAccountResult>("remove_account", { email });
  }

  // Logout current account - clear all authentication data
  static async logoutCurrentAccount(): Promise<LogoutResult> {
    return await invoke<LogoutResult>("logout_current_account");
  }
}
