import { invoke } from "@tauri-apps/api/core";
import type {
  Account<PERSON>istR<PERSON>ult,
  SwitchAccount<PERSON><PERSON>ult,
  Add<PERSON><PERSON>unt<PERSON><PERSON>ult,
  EditAccountResult,
  RemoveAccountR<PERSON>ult,
  LogoutResult
} from "../types/account";

export class AccountService {
  // Get all accounts with current account info
  static async getAccountList(): Promise<AccountListResult> {
    return await invoke<AccountListResult>("get_account_list");
  }

  // Real delete account API - calls cursor.com/api/dashboard/delete-account
  static async deleteAccount(): Promise<{ success: boolean; message: string }> {
    try {
      console.log('🔄 发送真实 API 请求到 cursor.com...');

      const response = await fetch('https://cursor.com/api/dashboard/delete-account', {
        method: 'POST',
        headers: {
          'Accept': '*/*',
          'Accept-Encoding': 'gzip, deflate, br, zstd',
          'Accept-Language': 'en,zh-CN;q=0.9,zh;q=0.8,eu;q=0.7',
          'Content-Type': 'application/json',
          'Content-Length': '2',
          'Origin': 'https://cursor.com',
          'Referer': 'https://cursor.com/cn/dashboard?tab=settings',
          'Sec-CH-UA': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
          'Sec-CH-UA-Arch': '"x86"',
          'Sec-CH-UA-Bitness': '"64"',
          'Sec-CH-UA-Mobile': '?0',
          'Sec-CH-UA-Platform': '"macOS"',
          'Sec-CH-UA-Platform-Version': '"15.3.1"',
          'Sec-Fetch-Dest': 'empty',
          'Sec-Fetch-Mode': 'cors',
          'Sec-Fetch-Site': 'same-origin',
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
          // Note: Cookie header would need to be added with actual session token
          // 'Cookie': 'WorkosCursorSessionToken=...; ph_phc_TXdpocbGVeZVm5VJmAsHTMrCofBQu3e0kN8HGMNGTVW_posthog=...'
        },
        body: JSON.stringify({}),
        mode: 'cors',
        credentials: 'include' // Include cookies for authentication
      });

      console.log(`📥 API 响应: ${response.status} ${response.statusText}`);
      console.log('Response Headers:', Object.fromEntries(response.headers.entries()));

      if (response.ok) {
        const responseText = await response.text();
        console.log('Response Body:', responseText);

        return {
          success: true,
          message: `✅ 删除账户请求成功！状态码: ${response.status}, 响应: ${responseText}`
        };
      } else {
        const errorText = await response.text();
        console.error('API Error Response:', errorText);

        return {
          success: false,
          message: `❌ 删除账户失败！状态码: ${response.status}, 错误: ${errorText || response.statusText}`
        };
      }
    } catch (error) {
      console.error('网络请求失败:', error);

      if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
        return {
          success: false,
          message: '❌ 网络请求失败！可能是 CORS 限制或网络连接问题。请检查网络连接或在浏览器开发者工具中查看详细错误。'
        };
      }

      return {
        success: false,
        message: `❌ 请求发生错误: ${error instanceof Error ? error.message : '未知错误'}`
      };
    }
  }

  // Add a new account
  static async addAccount(email: string, token: string, refreshToken?: string): Promise<AddAccountResult> {
    return await invoke<AddAccountResult>("add_account", {
      email,
      token,
      refresh_token: refreshToken || null
    });
  }

  // Switch to a different account
  static async switchAccount(email: string): Promise<SwitchAccountResult> {
    return await invoke<SwitchAccountResult>("switch_account", { email });
  }

  // Switch to account using email and token directly (improved method)
  static async switchAccountWithToken(
    email: string,
    token: string,
    authType?: string
  ): Promise<SwitchAccountResult> {
    return await invoke<SwitchAccountResult>("switch_account_with_token", {
      email,
      token,
      authType
    });
  }

  // Edit an existing account
  static async editAccount(
    email: string,
    newToken?: string,
    newRefreshToken?: string
  ): Promise<EditAccountResult> {
    return await invoke<EditAccountResult>("edit_account", {
      email,
      newToken: newToken || null,
      newRefreshToken: newRefreshToken || null
    });
  }

  // Remove an account
  static async removeAccount(email: string): Promise<RemoveAccountResult> {
    return await invoke<RemoveAccountResult>("remove_account", { email });
  }

  // Logout current account - clear all authentication data
  static async logoutCurrentAccount(): Promise<LogoutResult> {
    return await invoke<LogoutResult>("logout_current_account");
  }
}
